using System;
using UnityEngine;

public class CreatureMovement : MonoBehaviour
{
    private CreatureMovementData _data;
    private Transform _visualsTransform;
    
    private float _currentSpeed;
    
    [SerializeField]
    private float StoppingDistance = 0.25f;

    public CreatureMovementData Data;
    public Transform TargetPosition;

    private void Awake()
    {
        _visualsTransform = transform.GetChild(0);
    }

    private void Start()
    {
        Initialize(Data);
    }

    public void Initialize(CreatureMovementData data)
    {
        _data = data;
    }
    
    public void Update()
    {
        MoveTowardsTarget();
    }

    private void MoveTowardsTarget()
    {
        if (TargetPosition == null)
            return;
        
        if (Vector3.Distance(transform.position, TargetPosition.position) <= StoppingDistance)
            return;
        
        var direction = TargetPosition.position - transform.position;
        direction.y = 0;
        direction.Normalize();
        
        Move(direction);
    }

    public void Move(Vector3 direction)
    {
        if (direction == Vector3.zero)
            return;
        
        // lerp movespeed towards target speed
        _currentSpeed = Mathf.Lerp(_currentSpeed, _data.MoveSpeed, 1 / _data.Acceleration);
        
        var distanceToTravel = direction * _currentSpeed * Time.deltaTime;
        transform.position += distanceToTravel;
    }
}

[CreateAssetMenu(fileName = "CreatureMovementData", menuName = "Data/CreatureMovementData")]
public class CreatureMovementData : ScriptableObject
{
    public float MoveSpeed = 1f;
    public float Acceleration = 2f;
    public float StepSize = 0.05f;
    public float StepInterval = 0.05f;
    public float StepHeight = 0.01f;
}
