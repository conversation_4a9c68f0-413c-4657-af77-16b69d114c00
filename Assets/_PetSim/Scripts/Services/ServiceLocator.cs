using System;
using System.Collections.Generic;
using UnityEngine;

public class ServiceLocator
{
    private static readonly Dictionary<Type, object> TypeToServiceMap = new();

    public static T Locate<T>()
    {
        var type = typeof(T);
        return (T) Locate(type);
    }

    public static void Register<T>(T service)
    {
        var type = typeof(T);
        if (TypeToServiceMap.ContainsKey(type))
            Debug.LogWarning($"Overriding Service: {type.Name}");
        TypeToServiceMap[type] = service;
    }

    public static void Unregister<T>(T service)
    {
        var type = typeof(T);
        if (TypeToServiceMap.TryGetValue(type, out var currService))
        {
            if (service.Equals(currService))
                TypeToServiceMap.Remove(type);
        }
    }

    private static object Locate(Type type)
    {
        if (TypeToServiceMap.TryGetValue(type, out var service))
            return service;

        return default;
    }
}