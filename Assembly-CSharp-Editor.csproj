<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>9.0</LangVersion>
    <_TargetFrameworkDirectories>non_empty_path_generated_by_unity.rider.package</_TargetFrameworkDirectories>
    <_FullFrameworkReferenceAssemblyPaths>non_empty_path_generated_by_unity.rider.package</_FullFrameworkReferenceAssemblyPaths>
    <DisableHandlePackageFileConflicts>true</DisableHandlePackageFileConflicts>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{c50b1219-28c7-c099-f2e3-84fa6f07ee91}</ProjectGuid>
    <ProjectTypeGuids>{E097FAD1-6243-4DAD-9C02-E9B9EFC3FFC1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp-Editor</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\Bin\Debug\Assembly-CSharp-Editor\</OutputPath>
    <DefineConstants>UNITY_6000_0_24;UNITY_6000_0;UNITY_6000;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_2023_1_OR_NEWER;UNITY_2023_2_OR_NEWER;UNITY_2023_3_OR_NEWER;UNITY_6000_0_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_MARSHALLING_TESTS;ENABLE_VIDEO;ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;TEXTCORE_1_0_OR_NEWER;EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED;PLATFORM_STANDALONE_WIN;PLATFORM_STANDALONE;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_NVIDIA;ENABLE_AMD;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER;PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION;PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS;ENABLE_MONO;NET_4_6;NET_UNITY_4_8;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER;DOTWEEN;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER;UNITY_EDITOR_ONLY_COMPILATION</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169,0649</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="F:\Billy\Documents\Unity Repo\PetSim\Library\PackageCache\com.unity.collab-proxy\Lib\Editor\unityplastic.dll" />
    <Analyzer Include="F:\Billy\Documents\Unity Repo\PetSim\Library\PackageCache\com.unity.collab-proxy\Lib\Editor\log4netPlastic.dll" />
    <Analyzer Include="F:\Billy\Documents\Unity Repo\PetSim\Library\PackageCache\com.unity.collab-proxy\Lib\Editor\Unity.Plastic.Antlr3.Runtime.dll" />
    <Analyzer Include="F:\Billy\Documents\Unity Repo\PetSim\Library\PackageCache\com.unity.collab-proxy\Lib\Editor\Unity.Plastic.Newtonsoft.Json.dll" />
    <Analyzer Include="F:\Program Files\Unity\6000.0.24f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="F:\Program Files\Unity\6000.0.24f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="F:\Program Files\Unity\6000.0.24f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.UIToolkit.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets\TutorialInfo\Scripts\Editor\ReadmeEditor.cs" />
    <Reference Include="UnityEngine">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AMDModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.AMDModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GraphicsStateCollectionSerializerModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.GraphicsStateCollectionSerializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.HierarchyCoreModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.HierarchyCoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputForUIModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputForUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.MarshallingModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.MarshallingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.MultiplayerModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.MultiplayerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.NVIDIAModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.NVIDIAModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ShaderVariantAnalyticsModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.ShaderVariantAnalyticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.AccessibilityModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.AccessibilityModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.AdaptivePerformanceModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.AdaptivePerformanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.BuildProfileModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.BuildProfileModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CoreBusinessMetricsModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreBusinessMetricsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.EmbreeModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.EmbreeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GIModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.GIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GraphicsStateCollectionSerializerModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphicsStateCollectionSerializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GridAndSnapModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridAndSnapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GridModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.MultiplayerModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.MultiplayerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Physics2DModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.Physics2DModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.PhysicsModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.PropertiesModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.PropertiesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SafeModeModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.SafeModeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.ShaderFoundryModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.ShaderFoundryModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SketchUpModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.SketchUpModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SpriteMaskModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteMaskModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SpriteShapeModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteShapeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SubstanceModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.SubstanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TerrainModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.TerrainModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextRenderingModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextRenderingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TilemapModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.TilemapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TreeModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.TreeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIAutomationModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIAutomationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UmbraModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.UmbraModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.VFXModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.VFXModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.VideoModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.VideoModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.XRModule">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEngine\UnityEditor.XRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\Managed\UnityEditor.Graphs.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.WebGL.Extensions">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\PlaybackEngines\WebGLSupport\UnityEditor.WebGL.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.WindowsStandalone.Extensions">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\PlaybackEngines\WindowsStandaloneSupport\UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Android.Extensions">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\PlaybackEngines\AndroidPlayer\UnityEditor.Android.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.YamlDotNet">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\PackageCache\com.unity.visualscripting\Editor\VisualScripting.Core\Dependencies\YamlDotNet\Unity.VisualScripting.YamlDotNet.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\PackageCache\com.unity.collections\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll</HintPath>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\PackageCache\com.unity.ext.nunit\net40\unity-custom\nunit.framework.dll</HintPath>
    </Reference>
    <Reference Include="unityplastic">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\PackageCache\com.unity.collab-proxy\Lib\Editor\unityplastic.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Plastic.Antlr3.Runtime">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\PackageCache\com.unity.collab-proxy\Lib\Editor\Unity.Plastic.Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Plastic.Newtonsoft.Json">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\PackageCache\com.unity.collab-proxy\Lib\Editor\Unity.Plastic.Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="log4netPlastic">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\PackageCache\com.unity.collab-proxy\Lib\Editor\log4netPlastic.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.IonicZip">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\PackageCache\com.unity.visualscripting\Editor\VisualScripting.Core\Dependencies\DotNetZip\Unity.VisualScripting.IonicZip.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\PackageCache\com.unity.visualscripting\Runtime\VisualScripting.Flow\Dependencies\NCalc\Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.TextureAssets">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\PackageCache\com.unity.visualscripting\Editor\VisualScripting.Core\EditorAssetResources\Unity.VisualScripting.TextureAssets.dll</HintPath>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\PackageCache\com.unity.nuget.mono-cecil\Mono.Cecil.dll</HintPath>
    </Reference>
    <Reference Include="DemiEditor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Assets\Plugins\Demigiant\DemiLib\Core\Editor\DemiEditor.dll</HintPath>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Assets\Plugins\Demigiant\DOTween\DOTween.dll</HintPath>
    </Reference>
    <Reference Include="DOTweenPro">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Assets\Plugins\Demigiant\DOTweenPro\DOTweenPro.dll</HintPath>
    </Reference>
    <Reference Include="DemiLib">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Assets\Plugins\Demigiant\DemiLib\Core\DemiLib.dll</HintPath>
    </Reference>
    <Reference Include="DOTweenProEditor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Assets\Plugins\Demigiant\DOTweenPro\Editor\DOTweenProEditor.dll</HintPath>
    </Reference>
    <Reference Include="DOTweenEditor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Assets\Plugins\Demigiant\DOTween\Editor\DOTweenEditor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Types.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Gradle.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\mscorlib.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.dll</HintPath>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Core.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Runtime.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Microsoft.CSharp.dll</HintPath>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.DataSetExtensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.ComponentModel.Composition.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Transactions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\Microsoft.Win32.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\netstandard.dll</HintPath>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.AppContext.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Concurrent.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.NonGeneric.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Specialized.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.EventBasedAsync.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.TypeConverter.dll</HintPath>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Console.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Data.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Debug.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.FileVersionInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Process.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.StackTrace.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Tools.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TraceSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Drawing.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Dynamic.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Calendars.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Compression.ZipFile.dll</HintPath>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.DriveInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Watcher.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.IsolatedStorage.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.MemoryMappedFiles.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Pipes.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.UnmanagedMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Expressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Queryable.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Rtc">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Http.Rtc.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NameResolution.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NetworkInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Ping.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Requests.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Sockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebHeaderCollection.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.Client.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.dll</HintPath>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.DispatchProxy.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.ILGeneration.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.Lightweight.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Reader.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.ResourceManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Writer.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.CompilerServices.VisualC.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Handles.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Formatters.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Claims.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Csp.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Principal.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.SecureString.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Duplex">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Duplex.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.NetTcp">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.NetTcp.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Security">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.RegularExpressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Overlapped.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Thread.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.ThreadPool.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Timer.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.ReaderWriter.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlSerializer.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>F:\Program Files\Unity\6000.0.24f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TestRunner">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\UnityEngine.TestRunner.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TestRunner">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\UnityEditor.TestRunner.dll</HintPath>
    </Reference>
    <Reference Include="Unity.InputSystem.ForUI">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.InputSystem.ForUI.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Searcher.Editor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.Searcher.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Rider.Editor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.Rider.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.AI.Navigation">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.AI.Navigation.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipeline.Universal.ShaderLibrary">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.RenderPipeline.Universal.ShaderLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Shaders">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.RenderPipelines.Universal.Shaders.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Cinemachine.Editor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.Cinemachine.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Runtime.Shared">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.RenderPipelines.Core.Runtime.Shared.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Timeline.Editor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.Timeline.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.ShaderGraph.Editor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.ShaderGraph.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Timeline">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.Timeline.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Editor.Shared">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.RenderPipelines.Core.Editor.Shared.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\UnityEngine.UI.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.ShaderLibrary">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.RenderPipelines.Core.ShaderLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Unity.PlasticSCM.Editor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.PlasticSCM.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Multiplayer.Center.Editor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.Multiplayer.Center.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Splines">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.Splines.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Mathematics.Editor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.Mathematics.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.GPUDriven.Runtime">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.RenderPipelines.GPUDriven.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Cinemachine">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.Cinemachine.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Collections">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.Collections.dll</HintPath>
    </Reference>
    <Reference Include="PPv2URPConverters">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\PPv2URPConverters.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Rendering.LightTransport.Editor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.Rendering.LightTransport.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.Core">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.VisualScripting.Core.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Config.Runtime">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.RenderPipelines.Universal.Config.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Splines.Editor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.Splines.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Editor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.RenderPipelines.Universal.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Collections.Editor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.Collections.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.Shared.Editor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.VisualScripting.Shared.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.Core.Editor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.VisualScripting.Core.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Multiplayer.Center.Common">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.Multiplayer.Center.Common.dll</HintPath>
    </Reference>
    <Reference Include="Unity.AI.Navigation.Updater">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.AI.Navigation.Updater.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.Flow.Editor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.VisualScripting.Flow.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.TextMeshPro.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.SettingsProvider.Editor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.VisualScripting.SettingsProvider.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.State.Editor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.VisualScripting.State.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.AI.Navigation.Editor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.AI.Navigation.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Mathematics">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.Mathematics.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Editor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.RenderPipelines.Core.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Runtime">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.RenderPipelines.Universal.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.State">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.VisualScripting.State.dll</HintPath>
    </Reference>
    <Reference Include="Unity.AI.Navigation.Editor.ConversionSystem">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.AI.Navigation.Editor.ConversionSystem.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro.Editor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.TextMeshPro.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst.Editor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.Burst.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.Burst.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualStudio.Editor">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.VisualStudio.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Runtime">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.RenderPipelines.Core.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.Flow">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.VisualScripting.Flow.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Rendering.LightTransport.Runtime">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.Rendering.LightTransport.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.InputSystem">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.InputSystem.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.2D.Runtime">
      <HintPath>F:\Billy\Documents\Unity Repo\PetSim\Library\ScriptAssemblies\Unity.RenderPipelines.Universal.2D.Runtime.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Assembly-CSharp-firstpass.csproj">
      <Project>{6a84f6a9-cd73-5859-362e-43d7ce1f91f7}</Project>
      <Name>Assembly-CSharp-firstpass</Name>
    </ProjectReference>
    <ProjectReference Include="Assembly-CSharp.csproj">
      <Project>{c77ce1e7-eba3-3a5b-32b8-9dc4d38612ff}</Project>
      <Name>Assembly-CSharp</Name>
    </ProjectReference>
    <ProjectReference Include="Assembly-CSharp-Editor-firstpass.csproj">
      <Project>{04b56629-f5ea-4184-1c02-93a69df449c1}</Project>
      <Name>Assembly-CSharp-Editor-firstpass</Name>
    </ProjectReference>
    <ProjectReference Include="Services.csproj">
      <Project>{92823fc1-a0d1-c11a-c749-af8b910392dd}</Project>
      <Name>Services</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
